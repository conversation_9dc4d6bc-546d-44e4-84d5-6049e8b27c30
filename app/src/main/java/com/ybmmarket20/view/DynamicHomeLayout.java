package com.ybmmarket20.view;

import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;

import com.apkfuns.logutils.LogUtils;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;
import com.ybm.app.bean.NetError;
import com.ybm.app.common.SmartExecutorManager;
import com.ybm.app.utils.SpUtil;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.ModuleView;
import com.ybmmarket20.bean.ModuleViewBean;
import com.ybmmarket20.bean.ModuleViewItem;
import com.ybmmarket20.bean.RowsBean;
import com.ybmmarket20.bean.TabProductBean;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.ResultListener;
import com.ybmmarket20.constant.AppNetConfig;

import java.lang.reflect.Type;

/**
 * 首页动态模块,增加了缓存功能
 */
public class DynamicHomeLayout extends DynamicCommonLayout {
    private String key = "homelayout_cache";
    private boolean isRetry = false;
    private Gson gson;
    private Gson gson2;
    private String api;
    private DynamicCommonLayout header;
    // 标记是首页的homelayout，还是诊所专区的homelayout
    private String zhugeEventName;

    public DynamicHomeLayout(Context context) {
        super(context);
    }

    public DynamicHomeLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public DynamicHomeLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    protected void initViews() {
        super.initViews();
        SmartExecutorManager.getInstance().execute(new Runnable() {
            @Override
            public void run() {
                final ModuleViewBean obj = getCache();
                if (obj == null) {
                    LogUtils.d("获取缓存不成功");
                    return;
                }
                SmartExecutorManager.getInstance().executeUI(new Runnable() {
                    @Override
                    public void run() {
                        if (obj != null && obj.moduleCategory != null) {//更新内容部分
                            setData(obj.moduleCategory);
                        } else {
                            LogUtils.d("内容部分数据解析异常了-----");
                        }
                        if (obj != null && obj.noFollowDropDownModuleCategory != null && header != null) {
                            header.setData(obj.noFollowDropDownModuleCategory);
                        }
                    }
                });
            }
        });
    }

    public String getApi() {
        if (TextUtils.isEmpty(api)) {
            api = AppNetConfig.HOME_DYNAMIC;
        }
        return api;
    }

    public void setApi(String api) {
        this.api = api;
    }

    public void bindHeaderLayout(DynamicCommonLayout header) {
        this.header = header;
    }

    //从网络获取新数据
    public void getNewData() {
        getNewData(getApi(), null);
    }

    //从网络获取新数据
    public void getNewData(final ResultListener<Integer> listener) {
        getNewData(getApi(), listener);
    }

    //从网络获取新数据
    public void getNewData(String api, final ResultListener<Integer> listener) {
        if (TextUtils.isEmpty(api)) {
            return;
        }
        HttpManager.getInstance().postParser(api, new BaseResponse<ModuleViewBean>() {
            @Override
            public void onSuccess(final String content, BaseBean<ModuleViewBean> obj, ModuleViewBean data) {
                if (obj != null && obj.isSuccess()) {
                    if (listener != null) {
                        listener.result(true, null, 0);
                    }
                    if (data != null) {//更新内容部分

                        updateData(data.moduleCategory);
                    } else {
                        LogUtils.d("内容部分数据解析异常了-----");
                    }
                    if (data != null && data.noFollowDropDownModuleCategory != null && header != null) {
                        header.updateData(data.noFollowDropDownModuleCategory);
                    }
                } else {
                    if (listener != null) {
                        listener.result(true, null, 0);
                    }
                }
            }

            @Override
            public void onFailure(NetError error) {
                super.onFailure(error);
                if (listener != null) {
                    listener.result(false, null, 0);
                }
                if (error != null && error.errorCode == NetError.TIME_OUT_ERROR && !isRetry) {
                    getNewData();
                    isRetry = true;
                }
            }

            @Override
            public BaseBean json(String str, Type type) {
                try {
                    BaseBean<ModuleViewBean> object = getGson().fromJson(str, type);
                    if (object != null && object instanceof BaseBean) {
                        if (((BaseBean) object).isSuccess() && object.getData() != null) {
                            saveCache(getGson().toJson(object.getData()));
                        }
                    }
                    return object;
                } catch (Throwable e) {
                    e.printStackTrace();
                }
                return null;
            }
        });
    }

    public ModuleViewBean getCache() {
        String json = SpUtil.readString(getKey(), "");
        if (TextUtils.isEmpty(json)) {
            return null;
        }
        try {
            return getGson().fromJson(new String(com.xyy.app.rsa.Base64.decode(json)), ModuleViewBean.class);
        } catch (Throwable e) {
            e.printStackTrace();
            cleanCache();
            return null;
        }
    }

    protected void saveCache(String json) {
        if (!TextUtils.isEmpty(json)) {
            json = com.xyy.app.rsa.Base64.encode(json.getBytes());
            SpUtil.writeString(getKey(), json);
        }
    }

    private Gson getGson() {
        if (gson == null) {
            gson = new GsonBuilder().registerTypeHierarchyAdapter(ModuleView.class, (JsonDeserializer<ModuleView<?>>) (json, typeOfT, context) -> {
                ModuleView view = new ModuleView();
                if (json.isJsonObject()) {
                    JsonObject object = json.getAsJsonObject();
                    int moduleId = object.get("moduleId").getAsInt();
                    if (moduleId >= 2000) {//产品
                        if (moduleId == 2037) {
                            view = getGson2().fromJson(json, new TypeToken<ModuleView<TabProductBean>>() {
                            }.getType());
                        } else {
                            view = getGson2().fromJson(json, new TypeToken<ModuleView<RowsBean>>() {
                            }.getType());
                        }
                    } else {//不是产品
                        view = getGson2().fromJson(json, new TypeToken<ModuleView<ModuleViewItem>>() {
                        }.getType());
                    }
                }
                return view;
            }).create();
        }
        return gson;
    }

    private Gson getGson2() {
        if (gson2 == null) {
            gson2 = new Gson();
        }
        return gson2;
    }

    private String getKey() {
        return key + "_" + getContext().getClass().getSimpleName();
    }

    protected void cleanCache() {
        LogUtils.d("clean cache");
        SpUtil.writeString(getKey(), "");
    }

    public String getZhugeEventName() {
        return zhugeEventName;
    }

    public void setZhugeEventName(String zhugeEventName) {
        this.zhugeEventName = zhugeEventName;
    }
}
