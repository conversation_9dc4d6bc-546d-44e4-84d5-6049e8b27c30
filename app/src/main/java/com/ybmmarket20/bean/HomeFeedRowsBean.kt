package com.ybmmarket20.bean

import android.text.TextUtils
import com.ybmmarket20.adapter.*
import com.ybmmarket20.utils.AuditStatusSyncUtil

class HomeFeedRowsBean(
) : RowsBean(), IPriceType {
    override fun getItemType(): Int {
        return if (actPt != null
            && actPt.assembleStatus == 1
            && TextUtils.isEmpty(controlTitle)) GOODS_STATUS_SPELL_GROUP
        else GOODS_STATUS_NORMAL
    }

    override fun isOEMGoods(): Boolean = isOEM

    override fun isOEMSign(): Boolean = signStatus == 1

    override fun isControlAgreement(): Boolean = isControlAgreement == 1

    override fun isControlGoods(): Boolean = isControl == 1

    override fun isControlSign(): Boolean = showAgree == 1

    override fun isPurchaseGoods(): Boolean = isPurchase

    override fun isShowPrice(): Boolean = ((isOEMGoods() && !isOEMSign() && !isControlAgreement()) ||
            (!isOEMGoods() && isControlAgreement() && !isControlSign()) ||
            (isOEMGoods() && !isOEMSign() && isControlAgreement() && !isControlSign()))
        .not()
}