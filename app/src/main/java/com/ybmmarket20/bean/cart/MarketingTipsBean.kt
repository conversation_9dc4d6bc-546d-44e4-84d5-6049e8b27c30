package com.ybmmarket20.bean.cart

data class MarketingTipsBean(
        val appUrl: String? = "",
        val pcUrl: String? = "",
        val tagList: ArrayList<MarketingTipsTag?>? = arrayListOf(),
        val tips: String? = "",
        val tipsFontColor: String? = "",
        val titleUrlText: String? = "",
        //是否领取：true-已领，false-未领
        var haseReceived: Boolean,
        //券模板ID
        val couponTemplateId: String? = ""
)

data class MarketingTipsTag(
        val index: Int? = 1,
        val name:String? = "",
        /**
         *  1 : 可配置文字，背景，边框的颜色和透明度
         *  2 : 优惠券样式，只可配置文字样式，背景为 .9 图
         */
        val uiType:Int? = 1
)