<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:layout_weight="1"
    android:background="@color/white"
    android:orientation="vertical">

    <View
        android:layout_width="106dp"
        android:layout_height="59dp"
        android:layout_centerVertical="true"
        android:background="@drawable/price_range_text_bg"/>

    <RelativeLayout
        android:layout_width="105dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center">

        <TextView
            android:layout_marginTop="12dp"
            android:id="@+id/tv_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:text=""
            android:textColor="@color/detail_tv_color_ff2121"
            android:textSize="15sp"
            android:textStyle="bold"/>

        <TextView
            android:id="@+id/tv_range"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tv_price"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="3dp"
            android:text=""
            android:textColor="@color/text_9494A6"
            android:textSize="10sp"/>
    </RelativeLayout>
</RelativeLayout>