<?xml version="1.0" encoding="utf-8"?>
<com.ybmmarket20.view.SwipeMenuLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:card_view="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cart_swipe_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:orientation="horizontal"
    android:paddingBottom="@dimen/dimen_dp_10"
    card_view:ios="false"
    card_view:leftSwipe="true"
    card_view:swipeEnable="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_item"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:orientation="vertical">


        <View
            android:id="@+id/divider_top"
            android:layout_width="0dp"
            android:layout_height="4dp"
            android:background="#FFF2F3F4"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/line_top"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:layout_marginLeft="@dimen/dimen_dp_18"
            android:layout_marginBottom="@dimen/dimen_dp_4"
            android:background="@drawable/line_vertical"
            android:layerType="software"
            app:layout_constraintBottom_toTopOf="@id/cb_good"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/divider_top" />

        <CheckBox
            android:id="@+id/cb_good"
            android:layout_width="@dimen/dimen_dp_26"
            android:layout_height="@dimen/dimen_dp_26"
            android:layout_marginLeft="@dimen/dimen_dp_6"
            android:layout_marginTop="@dimen/dimen_dp_45"
            android:background="@drawable/selector_cart_check"
            android:button="@null"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/divider_top" />


        <ImageView
            android:id="@+id/iv_good"
            android:layout_width="@dimen/dimen_dp_84"
            android:layout_height="@dimen/dimen_dp_84"
            android:layout_marginLeft="@dimen/dimen_dp_36"
            android:layout_marginTop="@dimen/dimen_dp_10"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/divider_top"
            tools:background="@color/translucent_black_97" />

        <ImageView
            android:id="@+id/iv_marker"
            android:layout_width="@dimen/dimen_dp_84"
            android:layout_height="@dimen/dimen_dp_84"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/iv_good"
            app:layout_constraintLeft_toLeftOf="@id/iv_good"
            app:layout_constraintRight_toRightOf="@id/iv_good"
            app:layout_constraintTop_toTopOf="@id/iv_good"
            tools:background="@drawable/load_more"
            tools:visibility="visible" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/group_limited_time_premium"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:constraint_referenced_ids="bg_limited_time_premium,iv_top_limited_time_premium,tv_limited_time_premium_time,tv_price_prefix_received,tv_limited_time_premium_time_title"
            tools:visibility="visible" />

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/bg_limited_time_premium"
            android:layout_width="87dp"
            android:layout_height="87dp"
            android:layout_marginStart="@dimen/dimen_dp_34"
            android:layout_marginTop="@dimen/dimen_dp_9"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/divider_top"
            app:rv_strokeColor="@color/color_ff2121"
            app:rv_strokeWidth="1.5dp" />

        <ImageView
            android:id="@+id/iv_top_limited_time_premium"
            android:layout_width="wrap_content"
            android:layout_height="15dp"
            android:src="@drawable/icon_cart_top_limited_time_premium"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="@id/bg_limited_time_premium"
            app:layout_constraintStart_toStartOf="@id/bg_limited_time_premium"
            app:layout_constraintTop_toTopOf="@id/bg_limited_time_premium" />

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/tv_limited_time_premium_time"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:gravity="center"
            android:paddingBottom="2dp"
            android:textColor="@color/white"
            android:textSize="12dp"
            android:textStyle="bold"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/tv_limited_time_premium_time_title"
            app:layout_constraintStart_toStartOf="@id/bg_limited_time_premium"
            app:layout_constraintTop_toBottomOf="@id/bg_limited_time_premium"
            app:rv_backgroundColor="@color/color_ff2121"
            app:layout_constraintEnd_toStartOf="@id/tv_limited_time_premium_time_title"
            tools:text="13:59:12" />

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/tv_limited_time_premium_time_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:paddingBottom="2dp"
            android:text="后结束"
            android:textColor="@color/white"
            android:textSize="12dp"
            android:textStyle="bold"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="@id/bg_limited_time_premium"
            app:layout_constraintTop_toBottomOf="@id/bg_limited_time_premium"
            app:rv_backgroundColor="@color/color_ff2121" />


        <TextView
            android:id="@+id/tv_goods_status"
            android:layout_width="0dp"
            android:layout_height="@dimen/dimen_dp_21"
            android:background="@color/translucent_black_60"
            android:gravity="center"
            android:textColor="@color/white"
            android:textSize="@dimen/dimen_dp_12"
            app:layout_constraintBottom_toBottomOf="@id/iv_good"
            app:layout_constraintLeft_toLeftOf="@id/iv_good"
            app:layout_constraintRight_toRightOf="@id/iv_good"
            tools:text="失效" />


        <TextView
            android:id="@+id/tv_goods_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dimen_dp_10"
            android:layout_marginRight="@dimen/dimen_dp_14"
            android:ellipsize="end"
            android:maxLines="2"
            android:textColor="@color/color_292933"
            android:textSize="@dimen/dimen_dp_15"
            android:textStyle="bold"
            app:layout_constraintLeft_toRightOf="@id/iv_good"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@id/iv_good"
            tools:text="感康 复方氨酚烷胺片感康复方氨酚烷胺片 /12颗*1盒" />


        <TextView
            android:id="@+id/tv_effect"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:textColor="@color/color_676773"
            android:textSize="@dimen/dimen_dp_11"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintLeft_toLeftOf="@id/tv_goods_name"
            app:layout_constraintRight_toRightOf="@id/tv_goods_name"
            app:layout_constraintTop_toBottomOf="@id/tv_goods_name"
            tools:text="效期：2021-09-01" />


        <com.ybmmarket20.view.TagView
            android:id="@+id/rl_icon_type"
            android:layout_width="0dp"
            android:layout_height="@dimen/dimen_dp_13"
            android:layout_marginTop="3dp"
            android:layout_marginRight="10dp"
            android:visibility="gone"
            app:layout_constraintLeft_toLeftOf="@id/tv_goods_name"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_effect"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tv_price_prefix_received"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="到手价"
            android:textColor="@color/color_ff2121"
            android:textSize="10dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/tv_price"
            app:layout_constraintStart_toStartOf="@id/tv_goods_name"
            app:layout_constraintTop_toTopOf="@id/tv_price"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tv_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="2dp"
            android:layout_marginTop="3dp"
            android:text="¥7.50/盒"
            android:textColor="@color/color_ff2121"
            app:layout_constraintStart_toEndOf="@id/tv_price_prefix_received"
            app:layout_constraintTop_toBottomOf="@id/rl_icon_type"
            app:layout_goneMarginStart="0dp" />


        <TextView
            android:id="@+id/tv_discount_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dimen_dp_11"
            android:background="@drawable/bg_cart_discount_price"
            android:paddingLeft="@dimen/dimen_dp_5"
            android:paddingTop="@dimen/dimen_dp_2"
            android:paddingRight="@dimen/dimen_dp_5"
            android:paddingBottom="@dimen/dimen_dp_2"
            android:textColor="@color/white"
            android:textSize="@dimen/dimen_dp_10"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/tv_price"
            app:layout_constraintLeft_toRightOf="@id/tv_price"
            app:layout_constraintTop_toTopOf="@id/tv_price"
            tools:text="折后约￥6.50"
            tools:visibility="visible" />

        <!--标签 比上次购买时降  -->
        <com.ybmmarket20.view.TagView
            android:id="@+id/tv_depreciate_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_dp_4"
            android:textColor="@color/color_676773"
            android:textSize="@dimen/dimen_dp_11"
            android:visibility="gone"
            app:layout_constraintLeft_toLeftOf="@id/tv_goods_name"
            app:layout_constraintTop_toBottomOf="@id/tv_price"
            tools:text="比加入时降￥20.00"
            tools:visibility="gone" />

        <TextView
            android:id="@+id/iv_good_num_in_group"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="@dimen/dimen_dp_14"
            android:textColor="@color/color_676773"
            android:textSize="@dimen/dimen_dp_13"
            android:visibility="gone"
            app:layout_constraintBaseline_toBaselineOf="@id/tv_price"
            app:layout_constraintRight_toRightOf="parent"
            tools:text="x1"
            tools:visibility="visible" />

        <LinearLayout
            android:id="@+id/ll_time_out"
            android:layout_width="wrap_content"
            android:layout_height="19dp"
            android:layout_gravity="center_vertical"
            android:layout_marginTop="@dimen/dimen_dp_5"
            android:background="@drawable/bg_cart_skill"
            android:visibility="gone"
            app:layout_constraintLeft_toLeftOf="@id/tv_goods_name"
            app:layout_constraintTop_toBottomOf="@id/tv_depreciate_tips"
            tools:visibility="visible">

            <ImageView
                android:layout_width="@dimen/dimen_dp_39"
                android:layout_height="@dimen/dimen_dp_19"
                android:src="@drawable/icon_cart_skill" />

            <TextView
                android:id="@+id/tv_cart_countdown"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center"
                android:minWidth="@dimen/dimen_dp_110"
                android:paddingLeft="5dp"
                android:paddingRight="5dp"
                android:textColor="@color/color_ff2121"
                android:textSize="11dp"
                tools:text="距离结束还剩 1天22小时56分" />

        </LinearLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_product_coupons"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="10dp"
            android:background="@drawable/shape_f7f7f7_3dp"
            android:paddingHorizontal="6dp"
            android:paddingVertical="3dp"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@id/tv_goods_name"
            app:layout_constraintTop_toBottomOf="@id/ll_time_out"
            app:layout_goneMarginTop="8dp"
            tools:visibility="visible">

            <!--            <com.ybmmarket20.common.widget.RoundTextView-->
            <!--                android:id="@+id/tv_product_coupons"-->
            <!--                android:layout_width="wrap_content"-->
            <!--                tools:text="专品券"-->
            <!--                app:layout_constraintTop_toTopOf="parent"-->
            <!--                app:layout_constraintStart_toStartOf="parent"-->
            <!--                android:textColor="@color/color_f82324"-->
            <!--                android:textSize="11dp"-->
            <!--                app:rv_cornerRadius="3dp"-->
            <!--                android:paddingHorizontal="4dp"-->
            <!--                android:paddingVertical="1dp"-->
            <!--                android:visibility="gone"-->
            <!--                tools:visibility="visible"-->
            <!--                app:rv_backgroundColor="@color/color_F7F7F7"-->
            <!--                app:rv_strokeColor="@color/color_f82324"-->
            <!--                app:rv_strokeWidth="1dp"-->
            <!--                android:layout_height="wrap_content"/>-->

            <TextView
                android:id="@+id/tv_product_coupons_content"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="6dp"
                android:ellipsize="end"
                android:maxLines="2"
                android:textColor="@color/colors_555555"
                android:textSize="12dp"
                app:layout_constraintEnd_toStartOf="@id/ll_go_gather_orders"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="满100元93折，快来享受优惠吧！" />

            <LinearLayout
                android:id="@+id/ll_go_gather_orders"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/tv_go_gather_orders"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="凑单"
                    android:textColor="@color/color_f82324"
                    android:textSize="12dp" />

                <ImageView
                    android:id="@+id/iv_go_gather_orders_arrow"
                    android:layout_width="6dp"
                    android:layout_height="10dp"
                    android:layout_gravity="center"
                    android:layout_marginStart="2dp"
                    android:src="@drawable/icon_go_gather_order_red_arrow" />
            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/barrier"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:barrierDirection="bottom"
            app:constraint_referenced_ids="ll_time_out,cl_product_coupons" />

        <include
            android:id="@+id/layout_subtotal_and_edit"
            layout="@layout/item_cart_group_footer"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="130dp"
            android:layout_marginTop="@dimen/dimen_dp_10"
            android:layout_marginBottom="@dimen/dimen_dp_10"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/barrier"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tv_activity_single"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dimen_dp_5"
            android:layout_marginTop="@dimen/dimen_dp_10"
            android:layout_marginRight="@dimen/dimen_dp_10"
            android:ellipsize="end"
            android:lineSpacingExtra="@dimen/dimen_dp_2"
            android:maxLines="2"
            android:padding="@dimen/dimen_dp_4"
            android:background="@drawable/shape_2corner_fff7e9"
            android:textColor="@color/text_color_333333"
            android:textSize="@dimen/dimen_dp_10"
            android:visibility="gone"
            app:layout_constraintLeft_toLeftOf="@id/iv_good"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_limited"
            tools:text="单品满减 购物已满500件，可减200元。还差100件，可再减200元  购物已满500件，可减200元。还差100件，可再减200元"
            tools:visibility="visible" />


        <TextView
            android:id="@+id/tv_limit"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dimen_dp_5"
            android:layout_marginTop="@dimen/dimen_dp_10"
            android:layout_marginRight="@dimen/dimen_dp_10"
            android:background="@drawable/shape_2corner_fff7e9"
            android:textColor="@color/text_color_333333"
            android:ellipsize="end"
            android:maxLines="1"
            android:padding="@dimen/dimen_dp_4"
            android:textSize="@dimen/dimen_dp_10"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="@id/iv_good"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_activity_single"
            tools:text="限购商品,购物已满500件，可减200元。还差100件，可再减200元  购物已满500件，可减200元。还差100件，可再减200元"
            tools:visibility="visible" />
        <TextView
            android:id="@+id/tv_limited"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dimen_dp_5"
            android:layout_marginTop="@dimen/dimen_dp_10"
            android:layout_marginRight="@dimen/dimen_dp_10"
            android:background="@drawable/shape_2corner_fff7e9"
            android:ellipsize="end"
            android:lineSpacingExtra="@dimen/dimen_dp_2"
            android:maxLines="2"
            android:padding="@dimen/dimen_dp_5"
            android:textColor="@color/text_color_333333"
            android:textSize="@dimen/text_12"
            android:visibility="gone"
            app:layout_constraintLeft_toLeftOf="@id/iv_good"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/layout_subtotal_and_edit"
            tools:text="购买79盒及以下时可享受11.85元/盒，超过79盒的部分恢复12.25元/盒"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/line_bottom"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:layout_marginLeft="@dimen/dimen_dp_18"
            android:layout_marginTop="@dimen/dimen_dp_4"
            android:background="@drawable/line_vertical"
            android:layerType="software"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/cb_good" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <Button
        android:id="@+id/btnCollect"
        android:layout_width="50dp"
        android:layout_height="match_parent"
        android:background="#FDA430"
        android:clickable="true"
        android:text="收藏"
        android:textColor="@android:color/white"
        android:textSize="@dimen/dimen_dp_13" />

    <Button
        android:id="@+id/btn_find_same_goods"
        android:layout_width="50dp"
        android:layout_height="match_parent"
        android:background="#FD7121"
        android:clickable="true"
        android:text="找相似"
        android:textColor="@android:color/white"
        android:textSize="@dimen/dimen_dp_13" />

    <Button
        android:id="@+id/btnDelete"
        android:layout_width="50dp"
        android:layout_height="match_parent"
        android:background="#F74C4C"
        android:text="删除"
        android:textColor="@android:color/white"
        android:textSize="@dimen/dimen_dp_13" />


</com.ybmmarket20.view.SwipeMenuLayout>