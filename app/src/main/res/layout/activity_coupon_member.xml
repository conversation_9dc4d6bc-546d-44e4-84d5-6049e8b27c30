<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <RelativeLayout
        android:id="@+id/rl_title"
        style="@style/header_layout"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_back"
            style="@style/header_layout_left"
            android:src="@drawable/ic_back" />

        <TextView
            android:id="@+id/tv_title"
            style="@style/header_layout_mid"
            android:text="@string/my_coupon" />
    </RelativeLayout>

    <com.flyco.tablayout.SlidingTabLayout
        android:id="@+id/stl_my_coupon_list"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:background="@color/white"
        android:orientation="horizontal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/rl_title"
        app:tl_indicator_color="@color/base_colors_new"
        app:tl_indicator_corner_radius="2dp"
        app:tl_indicator_height="4dp"
        app:tl_indicator_width_equal_title="true"
        app:tl_tab_space_equal="true"
        app:tl_textAllCaps="true"
        app:tl_textBold="BOTH"
        app:tl_textSelectColor="@color/text_292933"
        app:tl_textSelectSize="17sp"
        app:tl_textUnselectColor="@color/text_898999"
        app:tl_textsize="15sp" />

    <com.ybmmarket20.view.NoScrollViewPager
        android:id="@+id/nvp_my_coupon_list"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/stl_my_coupon_list" />

</androidx.constraintlayout.widget.ConstraintLayout>