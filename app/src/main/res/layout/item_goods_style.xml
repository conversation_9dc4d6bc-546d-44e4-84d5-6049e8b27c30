<?xml version="1.0" encoding="utf-8"?>
<com.ybmmarket20.common.widget.RoundLinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="1dp"
    android:orientation="horizontal"
    app:rv_backgroundColor="@color/white">
    <!-- item_all_goods.xml 和 item_goods.xml 有一点差别，android:layout_marginBottom=和layout_marginTop。item_all_goods是全部药品页所用的布局-->

    <FrameLayout
        android:layout_width="110dp"
        android:layout_height="110dp"
        android:layout_gravity="center"
        android:background="@color/white">

        <ImageView
            android:id="@+id/icon"
            android:layout_width="110dp"
            android:layout_height="110dp"
            android:layout_gravity="center" />

        <ImageView
            android:id="@+id/iv_shop_mark"
            android:layout_width="110dp"
            android:layout_height="110dp"
            android:layout_gravity="center"
            android:scaleType="centerCrop"
            android:src="@drawable/transparent" />


        <com.ybmmarket20.view.PromotionTagView
            android:id="@+id/view_ptv"
            android:layout_width="110dp"
            app:subTitleTextSize="5dp"
            app:contentTextSize="8dp"
            android:layout_gravity="center"
            android:layout_height="110dp" />

        <TextView
            android:id="@+id/shop_no_limit_tv01"
            android:layout_width="45dp"
            android:layout_height="45dp"
            android:layout_gravity="center"
            android:background="@drawable/shop_limit01"
            android:gravity="center"
            android:text=""
            android:textColor="#ffffff"
            android:textSize="12dp"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tv_activity_price"
            style="@style/activity_price"
            android:layout_gravity="bottom"
            android:layout_marginLeft="35dp"
            android:gravity="center_vertical"
            android:text="第三方第三方第三方"
            android:visibility="gone" />

    </FrameLayout>

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical"
        android:paddingLeft="@dimen/normal_margin"
        android:paddingRight="@dimen/normal_margin"
        android:paddingTop="@dimen/normal_margin">

        <!-- 商品名称和规格布局 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <!--商品名字  如 阿莫西林胶囊-->
            <TextView
                android:id="@+id/shop_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_weight="1"
                android:ellipsize="end"
                android:maxLines="2"
                android:textColor="@color/brand_shop_name"
                android:textSize="15sp"
                tools:text="铝碳酸镁咀嚼片电视剧卡夫卡了的撒发生大范德萨发健康是福" />

            <ImageView
                android:id="@+id/iv_goods_item_delete"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:scaleType="centerCrop"
                android:src="@drawable/icon_delete_x"
                android:visibility="gone" />

            <LinearLayout
                android:id="@+id/shop_ck_ll"
                android:layout_width="33dp"
                android:layout_height="33dp"
                android:layout_alignParentRight="true"
                android:layout_gravity="center_vertical"
                android:gravity="center_horizontal"
                android:paddingTop="11dp"
                android:visibility="gone">

                <CheckBox
                    android:id="@+id/shop_ck"
                    style="@style/shoucangCheckboxTheme"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:clickable="false" />
            </LinearLayout>
        </LinearLayout>

        <!-- 厂-->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:orientation="horizontal">

            <com.ybmmarket20.common.widget.RoundTextView
                android:layout_width="15dp"
                android:layout_height="15dp"
                android:gravity="center"
                android:includeFontPadding="true"
                android:text="厂"
                android:textColor="@color/tag_head_text"
                android:textSize="10sp"
                app:rv_backgroundColor="@color/tag_head_background"
                app:rv_cornerRadius="1dp"
                app:rv_strokeColor="@color/tag_head_stroke"
                app:rv_strokeWidth="1dp" />

            <TextView
                android:id="@+id/tv_chang_name"
                style="@style/goods_list_item_text_small"
                android:layout_marginLeft="4dp"
                tools:text="xxxxxxxx" />
        </LinearLayout>

        <!--效-->
        <LinearLayout
            android:id="@+id/ll_validity_period"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:orientation="horizontal"
            android:visibility="visible">

            <com.ybmmarket20.common.widget.RoundTextView
                android:layout_width="15dp"
                android:layout_height="15dp"
                android:gravity="center"
                android:includeFontPadding="true"
                android:text="效"
                android:textColor="@color/tag_head_text"
                android:textSize="10sp"
                app:rv_backgroundColor="@color/tag_head_background"
                app:rv_cornerRadius="1dp"
                app:rv_strokeColor="@color/tag_head_stroke"
                app:rv_strokeWidth="1dp" />

            <TextView
                android:id="@+id/tv_validity_period"
                style="@style/goods_list_item_text_small"
                android:layout_marginLeft="4dp"
                tools:text="xxxxxxxx" />
        </LinearLayout>

        <!-- 供应商（和自营 显示 是互斥的） -->
        <LinearLayout
            android:id="@+id/ll_gong"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:orientation="horizontal">

            <com.ybmmarket20.common.widget.RoundTextView
                android:layout_width="15dp"
                android:layout_height="15dp"
                android:gravity="center"
                android:includeFontPadding="true"
                android:text="供"
                android:textColor="@color/tag_head_text"
                android:textSize="10sp"
                app:rv_backgroundColor="@color/tag_head_background"
                app:rv_cornerRadius="1dp"
                app:rv_strokeColor="@color/tag_head_stroke"
                app:rv_strokeWidth="1dp" />

            <TextView
                android:id="@+id/tv_open"
                style="@style/goods_list_item_text_small"
                android:layout_width="wrap_content"
                android:layout_marginLeft="4dp"
                android:drawableRight="@drawable/icon_open_shop"
                android:drawablePadding="2dp"
                tools:text="xxxxxx" />

        </LinearLayout>

        <!-- 自营 -->
        <LinearLayout
            android:id="@+id/ll_company_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginRight="10dp"
            android:layout_marginTop="6dp"
            android:orientation="horizontal">

            <com.ybmmarket20.common.widget.RoundTextView
                android:id="@+id/icon_cart_proprietary"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_gravity="bottom"
                android:layout_marginRight="4dp"
                android:gravity="center"
                android:includeFontPadding="true"
                android:paddingLeft="1.67dp"
                android:paddingRight="1.67dp"
                android:textSize="10sp"
                android:visibility="visible"
                app:rv_cornerRadius="1dp"
                app:rv_strokeColor="#8000B377"
                app:rv_backgroundColor="#0D00B377"
                android:textColor="#00B377"
                app:rv_strokeWidth="1dp"
                tools:text="自营" />

            <com.ybmmarket20.common.widget.RoundTextView
                android:id="@+id/icon_gross_margin"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_gravity="bottom"
                android:layout_marginRight="4dp"
                android:gravity="center"
                android:includeFontPadding="true"
                android:paddingLeft="1.67dp"
                android:paddingRight="1.67dp"
                android:text="高毛"
                android:textColor="@color/gross_margin_color"
                android:textSize="10sp"
                android:visibility="visible"
                app:rv_cornerRadius="1.33dp"
                app:rv_strokeColor="@color/gross_margin_color"
                app:rv_strokeWidth="0.5dp"
                tools:text="高毛" />

            <TextView
                android:id="@+id/tv_company_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:maxLines="1"
                android:singleLine="true"
                android:textColor="@color/text_9494A6"
                android:textSize="11sp"
                android:visibility="gone"
                tools:text="重庆小药药医药分公司" />


        </LinearLayout>

        <!-- 价格 销量  中包装布局-->
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:orientation="horizontal">

            <LinearLayout
                android:id="@+id/ll_tag1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_brand_control"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:singleLine="true"
                    android:text="暂无购买权限"
                    android:textColor="@color/brand_control"
                    android:textSize="16sp"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/tv_oem"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:ellipsize="end"
                    android:lines="1"
                    android:singleLine="true"
                    android:text="价格签署协议可见"
                    android:textColor="@color/brand_new_color"
                    android:textSize="13sp"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/shop_price"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:ellipsize="end"
                    android:lines="1"
                    android:singleLine="true"
                    android:textColor="@color/record_red"
                    android:textSize="17sp"
                    android:visibility="visible"
                    tools:text="¥55" />

                <com.ybmmarket20.view.UnderlineTextView
                    android:id="@+id/tv_original_price"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignBottom="@id/shop_price"
                    android:layout_marginBottom="1.5dp"
                    android:layout_marginLeft="4dp"
                    android:layout_toLeftOf="@+id/shop_price_tv"
                    android:layout_toRightOf="@id/shop_price"
                    android:ellipsize="end"
                    android:lines="1"
                    android:singleLine="true"
                    android:textColor="@color/text_9494A6"
                    android:textSize="11sp"
                    android:visibility="visible"
                    tools:text="50" />
            </LinearLayout>


            <TextView
                android:id="@+id/tv_sales_quantity"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignBottom="@id/ll_tag1"
                android:layout_marginBottom="2dp"
                android:layout_marginLeft="10dp"
                android:layout_toLeftOf="@+id/shop_price_tv"
                android:layout_toRightOf="@id/ll_tag1"
                android:ellipsize="end"
                android:singleLine="true"
                android:textColor="@color/brand_description_tv1"
                android:textSize="11sp"
                android:visibility="gone"
                tools:text="销量" />

            <TextView
                android:id="@+id/shop_price_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignBottom="@id/ll_tag1"
                android:layout_alignParentRight="true"
                android:layout_marginBottom="2dp"
                android:layout_marginRight="2dp"
                android:singleLine="true"
                android:textColor="@color/brand_description_tv1"
                android:textSize="10sp"
                android:visibility="visible"
                tools:text="中包装：10盒" />

        </RelativeLayout>

        <!--控销价零售价-->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            android:layout_marginTop="3dp"
            android:orientation="horizontal">

            <RelativeLayout
                android:id="@+id/shop_price_layout"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:visibility="invisible">

                <TextView
                    android:id="@+id/shop_price_kxj_title_tv"
                    style="@style/brand_item_kxj_lsj_base"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/bg_brand_item_control_market1"
                    android:ellipsize="end"
                    android:singleLine="true"
                    android:text="@string/product_list_kxj_title"
                    android:textColor="@color/detail_shop_price_kxj_title_tv"
                    android:visibility="visible" />

                <TextView
                    android:id="@+id/shop_price_kxj_number_tv"
                    style="@style/brand_item_kxj_lsj_base"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_toRightOf="@id/shop_price_kxj_title_tv"
                    android:background="@drawable/bg_brand_item_control_market2"
                    android:ellipsize="end"
                    android:singleLine="true"
                    android:text="12"
                    android:textColor="@color/detail_shop_price_kxj_title_tv"
                    android:visibility="visible" />


                <TextView
                    android:id="@+id/shop_price_ml_title_tv"
                    style="@style/brand_item_kxj_lsj_base"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="5dp"
                    android:layout_toRightOf="@id/shop_price_kxj_number_tv"
                    android:background="@drawable/bg_brand_item_control_market1"
                    android:ellipsize="end"
                    android:singleLine="true"
                    android:text="@string/product_list_ml_title"
                    android:textColor="@color/detail_shop_price_kxj_title_tv"
                    android:visibility="visible" />

                <TextView
                    android:id="@+id/shop_price_ml_number_tv"
                    style="@style/brand_item_kxj_lsj_base"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_toRightOf="@id/shop_price_ml_title_tv"
                    android:background="@drawable/bg_brand_item_control_market2"
                    android:ellipsize="end"
                    android:singleLine="true"
                    android:text="12"
                    android:textColor="@color/detail_shop_price_kxj_title_tv"
                    android:visibility="visible" />
            </RelativeLayout>

            <com.ybmmarket20.view.ProductEditLayout
                android:id="@+id/el_edit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="8dp" />

            <ImageView
                android:id="@+id/iv_remind"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:src="@drawable/selector_remind_src"
                android:visibility="gone" />
        </LinearLayout>

        <View
            android:id="@+id/view_line"
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:background="#F5F5F5" />

        <!--规格-->
        <LinearLayout
            android:id="@+id/ll_show_promotion"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical">

            <!--该TextView已被废弃-->
            <TextView
                android:id="@+id/tv_health_insurance"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginBottom="5dp"
                android:layout_marginTop="5dp"
                android:background="@drawable/bg_brand_item_health_insurance"
                android:paddingLeft="5dp"
                android:paddingRight="5dp"
                android:text="医保"
                android:textColor="@color/white"
                android:textSize="12.5sp"
                android:visibility="gone" />

            <com.ybmmarket20.view.TagView
                android:id="@+id/rl_icon_type"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginBottom="5dp"
                android:layout_marginTop="5dp"
                android:layout_weight="1" />

            <ImageView
                android:id="@+id/iv_service"
                style="@style/detail_iv_base"
                android:layout_gravity="center_vertical"
                android:gravity="center_vertical" />

            <!-- 这期功能暂时做不了，所以不显示-->
            <TextView
                android:id="@+id/tv_specification"
                style="@style/goods_list_item_text_small"
                android:layout_height="30dp"
                android:layout_marginRight="@dimen/normal_margin"
                android:drawableRight="@drawable/icon_putdown"
                android:gravity="center_vertical"
                android:text="规格：2种"
                android:visibility="gone" />
        </LinearLayout>

        <!--折叠出来的布局-->
        <ImageView
            android:id="@+id/iv_discover_tab_child_line"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:scaleType="fitXY"
            android:src="@drawable/icon_discover_pulldown_line"
            android:visibility="gone" />

    </LinearLayout>
</com.ybmmarket20.common.widget.RoundLinearLayout>