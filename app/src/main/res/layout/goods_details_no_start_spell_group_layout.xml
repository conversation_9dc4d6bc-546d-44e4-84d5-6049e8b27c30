<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ll_no_start_spell_group_root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:minHeight="45dp"
    android:visibility="gone">

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:background="@drawable/bg_goods_detail_no_start_spell_group"
        android:paddingStart="10dp"
        android:paddingTop="5dp"
        android:orientation="vertical">

        <!-- 第一行：拼团价 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="拼团价"
                android:textColor="@color/white"
                android:textSize="12sp" />

            <TextView
                android:id="@+id/tv_no_start_spell_group_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:textStyle="bold"
                tools:text="¥15.25" />

        </LinearLayout>

        <!-- 第二行：单价 -->
        <LinearLayout
            android:id="@+id/ll_no_start_unit_price_line"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="2dp"
            android:visibility="gone"
            tools:visibility="visible">

            <!-- 单价显示 -->
            <TextView
                android:id="@+id/tv_no_start_unit_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/white"
                android:textSize="11sp"
                android:background="@color/color_F5F5F5"
                android:paddingLeft="4dp"
                android:paddingRight="4dp"
                android:paddingTop="2dp"
                android:paddingBottom="2dp"
                android:visibility="gone"
                tools:text="单价"
                tools:visibility="visible" />

        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:background="@color/color_00A76E"
        android:gravity="center"
        android:minWidth="@dimen/dimen_dp_138"
        android:orientation="vertical"
        android:paddingLeft="12dp"
        android:paddingRight="12dp">

        <TextView
            android:id="@+id/tv_no_start_spell_group_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/color_F4E984"
            android:textSize="12sp"
            tools:text="03月21日 18:00 开抢" />

    </LinearLayout>

</LinearLayout>