<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <View
        android:id="@+id/bg_img"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_74"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginBottom="@dimen/dimen_dp_2"
        android:background="@color/color_FBFBFB" />

    <ImageView
        android:id="@+id/iv_goods"
        android:layout_width="@dimen/dimen_dp_60"
        android:layout_height="@dimen/dimen_dp_60"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_goneMarginTop="@dimen/dimen_dp_6"
        android:layout_marginStart="@dimen/dimen_dp_11"
        android:layout_marginEnd="@dimen/dimen_dp_11"
        android:layout_marginTop="@dimen/dimen_dp_11" />

    <ImageView
        android:id="@+id/iv_title_tag"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_16"
        app:layout_constraintBottom_toBottomOf="@+id/iv_yellow"
        app:layout_constraintStart_toStartOf="@+id/bg_img"
        android:scaleType="fitXY"
        android:src="@drawable/icon_spell_group_goods_home_red" />

    <TextView
        android:id="@+id/tv_price_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="拼团价"
        android:textColor="@color/white"
        android:textSize="@dimen/dimen_dp_8"
        android:layout_marginBottom="@dimen/dimen_dp_3"
        app:layout_constraintBottom_toBottomOf="@+id/iv_title_tag"
        app:layout_constraintStart_toStartOf="@+id/iv_title_tag"
        app:layout_constraintEnd_toStartOf="@+id/iv_yellow" />

    <ImageView
        android:id="@+id/iv_yellow"
        android:layout_width="@dimen/dimen_dp_50"
        android:layout_height="@dimen/dimen_dp_18"
        android:src="@drawable/icon_spell_group_goods_home_yellow"
        app:layout_constraintTop_toBottomOf="@+id/bg_img"
        android:scaleType="fitXY"
        app:layout_constraintEnd_toEndOf="parent" />

    <TextView
        android:id="@+id/tv_price"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="@+id/iv_yellow"
        app:layout_constraintBottom_toBottomOf="@id/iv_yellow"
        app:layout_constraintTop_toTopOf="@+id/iv_yellow"
        tools:text="￥26.70"
        android:textColor="#FE2021"
        android:textSize="@dimen/dimen_dp_7"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintEnd_toEndOf="@+id/iv_yellow"
        android:layout_marginStart="@dimen/dimen_dp_10" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/group_price"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="iv_title_tag, tv_price_title, iv_yellow, tv_price" />

    <TextView
        android:id="@+id/tv_aptitude"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_16"
        app:layout_constraintBottom_toBottomOf="@+id/iv_yellow"
        app:layout_constraintStart_toStartOf="@+id/bg_img"
        android:text="价格认证资质可见"
        android:textSize="@dimen/dimen_dp_9"
        android:textColor="@color/white"
        android:gravity="center"
        tools:visibility="invisible"
        android:background="@drawable/icon_spell_group_goods_home_red" />

    <TextView
        android:id="@+id/tv_goods_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        tools:text="三九感冒灵颗粒"
        app:layout_constraintTop_toBottomOf="@+id/iv_yellow"
        android:textSize="@dimen/dimen_dp_12"
        android:textColor="@color/color_292933"
        android:maxLines="2"
        android:ellipsize="end"
        android:layout_marginTop="@dimen/dimen_dp_4" />

    <TextView
        android:id="@+id/tv_goods_spec"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="@dimen/dimen_dp_11"
        android:textColor="@color/color_9494A6"
        app:layout_constraintTop_toBottomOf="@+id/tv_goods_name"
        android:visibility="gone"
        android:singleLine="true"
        tools:text="50mg*1盒" />

    <TextView
        android:id="@+id/tv_spell_group_count"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="@dimen/dimen_dp_10"
        android:textColor="#fe2021"
        app:layout_constraintTop_toBottomOf="@+id/tv_goods_name"
        android:layout_marginTop="@dimen/dimen_dp_3"
        android:singleLine="true"
        tools:text="99999人已拼团" />

    <androidx.legacy.widget.Space
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_5"
        app:layout_constraintTop_toBottomOf="@+id/tv_spell_group_count"
        android:textSize="@dimen/dimen_dp_10" />

</androidx.constraintlayout.widget.ConstraintLayout>