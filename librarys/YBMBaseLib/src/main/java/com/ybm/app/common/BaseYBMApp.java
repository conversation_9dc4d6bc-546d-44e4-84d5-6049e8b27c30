package com.ybm.app.common;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Process;

import androidx.arch.core.executor.ArchTaskExecutor;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import android.util.DisplayMetrics;
import android.view.WindowManager;

import com.apkfuns.logutils.LogLevel;
import com.apkfuns.logutils.LogUtils;
import com.ybm.app.bean.DeviceEntity;
import com.ybm.app.bean.PushBean;
import com.ybm.app.utils.BugUtil;
import com.ybm.app.utils.JsonUtils;
import com.ybm.app.utils.SpUtil;
import com.ybm.app.utils.Utils;


/**
 * 基类
 */
public abstract class BaseYBMApp extends Application {
    public static final String APPISBACKGROUND = "INTENT_ACTION_APPISBACKGROUND";//前台进入后台
    public static final String APPISFOREGROUND = "INTENT_ACTION_APPISFOREGROUND";//从后台进入前台
    public static final String KEY_DEVICEENTITY = "key_deviceentity";
    private static BaseYBMApp appLike = null;
    private static Application app = null;
    private static boolean isSetAppDpi;
    private Activity currActivity;
    private int activityCount = 0;
    private String lastActivityName = "";
    public static Handler handler;
    private DeviceEntity deviceEntity;
    private boolean isForeground = false;
    private boolean isCreate = false;
    public static long appBgTime = 0; //app 进入后台时间

    @Override
    protected void attachBaseContext(Context base) {
        super.attachBaseContext(base);
        app = this;
        appLike = this;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        if (isMainProcess()) {
            handler = new Handler(Looper.getMainLooper());
            app.registerActivityLifecycleCallbacks(activityLifecycle);
            debugMode();
            initSDKBg();
            initSDK();
            isCreate = true;
            if (isSetDpi()) {
                setAppDpi();
            }
        }
    }

    /**
     * 未同意隐私协议前，不能使用ActivityManager方法来判断进程
     */
    private boolean isMainProcess(){
        if(isAgreedPrivacy()){
            return getPackageName().equals(Utils.getCurrentProcessName(this));
        }else{
            return getPackageName().equals(Utils.getCurrentProcessNameNoPrivacy());
        }
    }

    //第三方SDK加载
    private final void initSDK() {
        initSDKMainProcess();
    }

    //工作线程加载第三方sdk
    @SuppressLint("RestrictedApi")
    private final void initSDKBg(){
        ArchTaskExecutor.getInstance().executeOnDiskIO(new Runnable() {
            @Override
            public void run() {
                Process.setThreadPriority(Process.THREAD_PRIORITY_BACKGROUND);
                LogUtils.getLogConfig()
                        .configAllowLog(isDebug())
                        .configTagPrefix("YBM-")
                        .configShowBorders(true)
                        .configLevel(LogLevel.TYPE_VERBOSE);
                getDeviceEntityBg(app);
                initSDKMainProcessBg();
            }
        });
    }

    private final void debugMode() {
        if (isDebug()) {
//            StrictMode.setThreadPolicy(new StrictMode.ThreadPolicy.Builder().detectDiskReads().detectDiskWrites().detectCustomSlowCalls().penaltyLog().build());
//            StrictMode.setVmPolicy(new StrictMode.VmPolicy.Builder().detectAll().penaltyLog().build());
            initDebugMode();
        }
    }

    public abstract boolean isDebug();

    public abstract boolean isAgreedPrivacy();

    /**
     * 主进程初始化调式工具
     *
     * @return
     */
    protected void initDebugMode() {

    }

    /**
     * 主进程Ui线程初始化sdk
     *
     * @return
     */
    protected void initSDKMainProcess() {

    }

    /**
     * 主进程工作线程初始化sdk
     *
     * @return
     */
    protected void initSDKMainProcessBg() {

    }

    /**
     * 自动设置dpi
     *
     * @return
     */
    protected boolean isSetDpi() {
        return true;
    }

    public abstract void handlerPush(String content, int type, int from, PushBean bean);

    public static BaseYBMApp getApp() {
        return appLike;
    }

    public static Application getAppContext() {
        return app;
    }


    //统计当前activity 与app的前后台切换动作
    private Application.ActivityLifecycleCallbacks activityLifecycle = new Application.ActivityLifecycleCallbacks() {

        @Override
        public void onActivityCreated(Activity activity, Bundle savedInstanceState) {
            if (currActivity == null) {//防止应用回收的bug
                currActivity = activity;
            }
        }

        @Override
        public void onActivityStarted(Activity activity) {
            activityCount++;
        }

        @Override
        public void onActivityResumed(Activity activity) {
            if (activityCount == 1 && !isForeground) {
                if (isCreate) {
//                    UiUtils.toast("第一次打开进入前台");
                    appBgTime = System.currentTimeMillis();
                    Intent intent = new Intent(APPISFOREGROUND);
                    intent.putExtra("isFirst", true);
                    LocalBroadcastManager.getInstance(app).sendBroadcast(intent);
                    isCreate = false;
                } else {
                    appBgTime = System.currentTimeMillis();
                    Intent intent = new Intent(APPISFOREGROUND);
                    intent.putExtra("isFirst", false);
                    LocalBroadcastManager.getInstance(app).sendBroadcast(intent);
//                    UiUtils.toast("后台进入前台");
                }
            }
            isForeground = true;
            if (currActivity != null) {
                lastActivityName = currActivity.getClass().getName();
            }
            currActivity = activity;
        }

        @Override
        public void onActivityPaused(Activity activity) {

        }

        @Override
        public void onActivityStopped(Activity activity) {
            activityCount--;
            if (activityCount <= 0) {
                activityCount = 0;
                isForeground = false;
                appBgTime = System.currentTimeMillis();
                LocalBroadcastManager.getInstance(app).sendBroadcast(new Intent(APPISBACKGROUND));
//                UiUtils.toast("进入后台");
            }
        }

        @Override
        public void onActivitySaveInstanceState(Activity activity, Bundle outState) {

        }

        @Override
        public void onActivityDestroyed(Activity activity) {

        }
    };

    //当前活动的activity
    public Activity getCurrActivity() {
        return currActivity;
    }


    //上个页面的activity的名字
    public String getLastActivityName() {
        if (lastActivityName == null) {
            return "";
        }
        return lastActivityName;
    }

    //是否前台运行
    public boolean isForeground() {
        return isForeground;
    }

    public DeviceEntity getDeviceEntity() {
        if (deviceEntity == null) {
            String str = SpUtil.readString(KEY_DEVICEENTITY, null);
            if (str != null) {
                try {
                    deviceEntity = JsonUtils.fromJson(str, DeviceEntity.class);
                } catch (Exception e) {
                    deviceEntity = new DeviceEntity(getAppContext());
                }
            } else {
                deviceEntity = new DeviceEntity(getAppContext());
            }
        }
        return deviceEntity;
    }

    //是否低端机型
    public boolean isLowDevice() {
        getDeviceEntity();
        if ((deviceEntity.cpu_core <= 2 && deviceEntity.sysMemory <= 1024 * 1024 * 1024 * 1) || deviceEntity.width < 720) {//内存1GB以下，双核以下为低端设备,或者屏幕小于720p的
            return true;
        } else {
            return false;
        }
    }

    //后台加载设备信息
    private final void getDeviceEntityBg(final Context context) {
        deviceEntity = new DeviceEntity(context);
        if (deviceEntity != null) {
            LogUtils.d("手机系统：" + deviceEntity.os);
            SpUtil.writeString(KEY_DEVICEENTITY, JsonUtils.toJson(deviceEntity));
        }
    }

    //设置app的dpi值
    public static boolean setAppDpi() {
        Context context = BaseYBMApp.getAppContext();
        if (context == null) {
            return false;
        }
        if (context.getResources() == null) {
            return false;
        }
        if (context.getResources().getDisplayMetrics() == null) {
            return false;
        }
        try {
            DisplayMetrics displayMetrics = context.getResources().getDisplayMetrics();
            float density = displayMetrics.widthPixels * 1.0f / 360;
            int densityDpi = (int) (density * 160);
            if (density <= 0.5 || densityDpi < 120) {//太小不修改
                return false;
            }
            if (Math.abs(densityDpi - displayMetrics.densityDpi) > 5 || Math.abs(density - displayMetrics.density) > 0.2) {//不相同
                LogUtils.d("修改dpi: 原" + displayMetrics.densityDpi + " 修改为：" + densityDpi);
            } else {
            }
            try {
                displayMetrics.density = density;
                displayMetrics.densityDpi = densityDpi;
                displayMetrics.scaledDensity = density;
                isSetAppDpi = true;
                return true;
            } catch (Throwable e) {
                BugUtil.sendBug(e);
                //默认设置
                resetAppDpi();
            }
        } catch (Throwable e) {
            BugUtil.sendBug(e);
        }
        return false;
    }

    //重置app的dpi值
    public static boolean resetAppDpi() {
        if (getAppContext() == null) {
            return false;
        }
        if (getAppContext().getResources() == null) {
            return false;
        }
        if (getAppContext().getResources().getDisplayMetrics() == null) {
            return false;
        }
        if (!isSetAppDpi) {
            return false;
        }
        try {
            DisplayMetrics displayMetrics = getAppContext().getResources().getDisplayMetrics();
            WindowManager windowManager = (WindowManager) getAppContext().getSystemService(Context.WINDOW_SERVICE);
            windowManager.getDefaultDisplay().getMetrics(displayMetrics);
        } catch (Throwable e1) {
            BugUtil.sendBug(e1);
        }
        return false;
    }
}
